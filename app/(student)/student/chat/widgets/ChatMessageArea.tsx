'use client';
import './chatStyles.module.css';

import React, { useRef, useEffect, useState, useCallback, useLayoutEffect, useMemo } from 'react';
import { useChatContext } from '../_logics/chat_context';
import { useTeacherTheme } from '@/components/general/TeacherTheme/TeachersTheme';
import { WebSocketService } from '../_logics/websocketService';
import { useRouter } from 'next/navigation';
import { useChatWebSocket } from '../_logics/useChatWebSocket';
import { InviteToGroupModal } from './InviteToGroupModal';
import { BulkInviteModal } from './BulkInviteModal';
import { VariableSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { format, isToday, isYesterday } from 'date-fns';
import { GroupMember, MessageRowProps } from '../_logics/chat_types';
import { ChatBubble } from '../components/ChatBubble';
import { WelcomeScreen } from '../components/WelcomeScreen';

const MessageRow = ({ index, style, data }: MessageRowProps) => {
  const { messages, currentUserId, isDark } = data;
  const message = messages[index];
  const prevMessage = index > 0 ? messages[index - 1] : null;
  const nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
  const isCurrentUser = message.senderId === currentUserId;
  const showHeader = !prevMessage || prevMessage.senderId !== message.senderId;
  const showTail = !nextMessage || nextMessage.senderId !== message.senderId;
  
  // Check if this is the first message in a group or the last
  const isFirstInGroup = showHeader;
  const isLastInGroup = showTail;
  const isSingleMessage = isFirstInGroup && isLastInGroup;

  const getContainerStyle = () => ({
    display: 'flex',
    flexDirection: 'column' as const,
    alignItems: isCurrentUser ? 'flex-end' : 'flex-start',
    width: '100%',
    paddingLeft: isCurrentUser ? '20%' : '8px',
    paddingRight: isCurrentUser ? '80px' : '20%',
    marginBottom: isLastInGroup ? '8px' : '1px',
  });

  return (
    <div style={style}>
      <div style={getContainerStyle()}>
        <ChatBubble
          isCurrentUser={isCurrentUser}
          isDark={isDark}
          isFirstInGroup={isFirstInGroup}
          isLastInGroup={isLastInGroup}
          isSingleMessage={isSingleMessage}
          showHeader={showHeader}
          senderName={message.senderName}
          content={message.content}
          timestamp={message.timestamp}
          status={message.status}
        />
      </div>
    </div>
  );
};

export const ChatMessageArea = () => {
  const { state, fetchRoomMembers, leaveGroup, currentUser, startDirectChat } = useChatContext();
  // establish websocket for active room to get live messages
  useChatWebSocket(state.activeRoomId);
  const { isDark } = useTeacherTheme();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const activeRoom = state.rooms.find(room => room.id === state.activeRoomId);
  const rawMessages = state.activeRoomId ? state.messages[state.activeRoomId] || [] : [];
  const [groupMembers, setGroupMembers] = useState<any[]>([]);

  // Build a quick map of memberId -> name for current room
  const memberMap = useMemo(() => {
    const map: Record<string, string> = {};
    const allMembers = activeRoom?.members || [];
    allMembers.forEach(m => { map[m.id] = m.name; });
    groupMembers.forEach(m => { map[m.id] = m.name; });
    return map;
  }, [activeRoom?.members, groupMembers]);

  // Enrich messages with senderName fallback
  const messages = useMemo(() => {
    return rawMessages.map(msg => ({
      ...msg,
      senderName: msg.senderName || memberMap[msg.senderId] || 'Unknown'
    }));
  }, [rawMessages, memberMap]);
  
  // Check if current user is an admin of the active room
  const isCurrentUserAdmin = activeRoom?.members.some(member => 
    member.isAdmin && member.id === state.user?.id
  ) || false;
  
  // State management
  const [isSearchActive, setIsSearchActive] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showInfoDrawer, setShowInfoDrawer] = useState(false);
  
  const [loadingMembers, setLoadingMembers] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [showLeaveConfirm, setShowLeaveConfirm] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showBulkInviteModal, setShowBulkInviteModal] = useState(false);
  const router = useRouter();
  
  // Monitor WebSocket connection status
  useEffect(() => {
    const websocketService = WebSocketService.getInstance();
    
    // Update status immediately
    setConnectionStatus(websocketService.getConnectionState());
    
    // Subscribe to connection status changes
    const handleConnectionStatus = (status: string) => {
      setConnectionStatus(status);
    };
    
    websocketService.addConnectionHandler(handleConnectionStatus);
    
    // Clean up on unmount
    return () => {
      websocketService.removeConnectionHandler(handleConnectionStatus);
    };
  }, []);

  // Keep track if first render to jump to bottom once
  const firstRenderRef = useRef(true);

  // Build items array with date headers
  const displayItems = useMemo(() => {
    const items: any[] = [];
    let lastDate = '';
    messages.forEach((msg, i) => {
      const dateObj = new Date(msg.timestamp);
      const dateKey = dateObj.toDateString();
      if (dateKey !== lastDate) {
        let label = '';
        if (isToday(dateObj)) label = 'Today';
        else if (isYesterday(dateObj)) label = 'Yesterday';
        else label = format(dateObj, 'EEEE, MMMM d, yyyy');
        items.push({ type: 'header', id: `header-${dateKey}`, label });
        lastDate = dateKey;
      }
      items.push({ type: 'message', id: msg.id || `msg-${i}`, msgIndex: i });
    });
    return items;
  }, [messages]);

  // Auto-scroll to bottom when messages change
  useLayoutEffect(() => {
    if (listRef.current) {
      listRef.current.scrollToItem(displayItems.length - 1, 'end');
    }
  }, [messages]);

    // Row renderer to handle date headers & messages
  const RowRenderer = ({ index, style, data }: any) => {
    const item = data.displayItems[index];
    if (item.type === 'header') {
      return (
        <div style={{ ...style, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <span style={{ fontSize: '13px', fontWeight: 500, color: isDark ? '#d1d5db' : '#6b7280' }}>
            {item.label}
          </span>
        </div>
      );
    }
    return (
      <MessageRow
        index={item.msgIndex}
        style={style}
        data={{
          messages: data.messages,
          currentUserId: data.currentUserId,
          isDark: data.isDark,
        }}
      />
    );
  };

  // Auto-scroll for non-virtualized fallback (messagesEndRef)
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Track the currently loaded room ID to prevent unnecessary refetches
  const [loadedRoomId, setLoadedRoomId] = useState<string | null>(null);

  // Scroll to bottom when switching rooms and fetch new members
  useEffect(() => {
    if (!state.activeRoomId || state.activeRoomId === loadedRoomId) {
      return; // Skip if no active room or already loaded
    }
    
    console.log('Active room changed to:', state.activeRoomId);
    setGroupMembers([]);
    setLoadingMembers(true);
    setShowInfoDrawer(false);
    setLoadedRoomId(state.activeRoomId);

    // Fetch members for the new room
    fetchGroupMembers(state.activeRoomId);
    firstRenderRef.current = true;
  }, [state.activeRoomId, loadedRoomId]);

  // Handle click outside dropdown
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (dropdownOpen && !target.closest('.dropdown-toggle')) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [dropdownOpen]);

  // Show welcome screen when no room is selected
  if (!state.activeRoomId || !activeRoom) {
    return <WelcomeScreen isDark={isDark} />;
  }

  // Handle search functionality
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    // TODO: maybe when am done i will don this , Implement search
  };
  

  // Function to fetch group members
  const fetchGroupMembers = async (roomId: string) => {
    if (!roomId) return;

    console.log(`Starting to fetch members for room: ${roomId}`);
    try {
      const members = await fetchRoomMembers(roomId);
      // Map the API response to our GroupMember type
      const formattedMembers: GroupMember[] = (members || []).map(member => ({
        id: member.id,
        name: member.name,
        avatar: member.avatar,
        isOnline: member.isOnline,
        isAdmin: member.is_admin,
        userType: member.user_type as 'student' | 'teacher' | 'admin',
        email: member.email,
        directChat: false // Add default value for directChat
      }));
      setGroupMembers(formattedMembers);
    } catch (error) {
      console.error(`Error fetching members for room ${roomId}:`, error);
      setGroupMembers([]);
    } finally {
      setLoadingMembers(false);
    }
  };

  // Handle menu actions
  const handleMenuAction = (action: string) => {
    setDropdownOpen(false);

    switch(action) {
      case 'info':
        setShowInfoDrawer(true);
        if (state.activeRoomId) {
          fetchGroupMembers(state.activeRoomId);
        }
        break;
      case 'clear':
        console.log(`Clearing chat history for: ${activeRoom?.name}`);
        break;
      case 'close':
        console.log(`Closing the current chat: ${activeRoom?.name}`);
        break;
      case 'exit':
        console.log(`Exiting the ${activeRoom?.isGroup ? 'group' : 'page'}: ${activeRoom?.name}`);
        break;
    }
  };
  
  // Toggle dropdown menu
  const toggleDropdown = () => setDropdownOpen(!dropdownOpen);
  
  // Handle menu selection
  const handleMenuSelect = (action: string) => {
    setDropdownOpen(false);
    handleMenuAction(action);
  };

  // Get connection status styling
  const getConnectionStatusStyles = () => {
    const baseStyles = 'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium';
    
    switch(connectionStatus) {
      case 'connected':
        return `${baseStyles} ${isDark ? 'bg-green-500 bg-opacity-20 text-green-300' : 'bg-green-100 text-green-800'}`;
      case 'connecting':
      case 'reconnecting':
        return `${baseStyles} ${isDark ? 'bg-yellow-500 bg-opacity-20 text-yellow-300' : 'bg-yellow-100 text-yellow-800'}`;
      default:
        return `${baseStyles} ${isDark ? 'bg-red-500 bg-opacity-20 text-red-300' : 'bg-red-100 text-red-800'}`;
    }
  };

  // Get connection status text
  const getConnectionStatusText = () => {
    const groupName = activeRoom?.name || 'chat group';
    const statusText = (() => {
      switch(connectionStatus) {
        case 'connected': return `Connected to chat group: ${groupName}`;
        case 'connecting': return 'Connecting...';
        case 'reconnecting': return 'Reconnecting...';
        default: return 'Disconnected, check your network';
      }
    })();
    
    return (
      <div className="flex items-center text-xs">
        <span className="font-medium">{statusText}</span>
      </div>
    );
  };

  // Get connection status dot color
  const getConnectionDotColor = () => {
    switch(connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting':
      case 'reconnecting': return 'bg-yellow-500';
      default: return 'bg-red-500';
    }
  };
  
  // Format member count
  const formatMemberCount = (count: number) => {
    if (count < 1000) return count.toString();
    return `${(count / 1000).toFixed(1)}k`;
  };

  // Get online user count for active room
  const onlineCount = state.activeRoomId 
    ? (state.onlineUsers[state.activeRoomId] || []).length
    : 0;

  // Get total member count for active room
  const memberCount = activeRoom?.memberCount || 0;

  return (
    <div className={`flex-1 flex flex-col h-full relative ${isDark ? 'bg-[#0b141a] text-white' : 'bg-[#efeae2] text-gray-800'}`}>
      {/* Header */}
      <div className={`${isDark ? 'bg-[#2a2f32] border-[#384058]' : 'bg-[#f0f2f5] border-gray-200'} border-b p-3 py-2 flex items-center justify-between sticky top-0 z-20`}>
        <div className="flex items-center">
          <img 
            src={activeRoom.avatar} 
            alt={activeRoom.name} 
            className="w-10 h-10 rounded-full object-cover shadow-md border border-gray-200"
            onError={(e) => {
              e.currentTarget.src = '/logo.png';
            }}
          />
          <div className="ml-3">
            <div className="flex items-center">
              <h2 className={`text-lg font-medium ${isDark ? 'text-white' : 'text-gray-900'}`}>
                {activeRoom.name}
              </h2>
              <span 
                className={`${getConnectionStatusStyles()} ml-2`}
                title={`Connection status: ${connectionStatus}`}
              >
                <span className={`w-2 h-2 rounded-full mr-1 ${getConnectionDotColor()}`}></span>
                {getConnectionStatusText()}
              </span>
            </div>
            <div className={`flex items-center text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              <span className="inline-block w-2 h-2 bg-[#006060] rounded-full mr-1"></span>
              Online
              <span className="ml-2">• {onlineCount} of {memberCount} online</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="p-1.5 rounded-full text-teal-500 hover:bg-teal-500/10 transition-colors"
            onClick={() => setShowInviteModal(true)}
            aria-label="Invite to group"
            title="Invite to group"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
          </button>
          {/* Bulk invite button */}
          <button
            className="p-1.5 rounded-full text-teal-500 hover:bg-teal-500/10 transition-colors"
            onClick={() => setShowBulkInviteModal(true)}
            aria-label="Bulk invite users"
            title="Bulk invite users"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
          </button>

          {/* <button
            className={`${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'} p-1.5 rounded-full ${isSearchActive ? 'bg-[#006060]/10' : ''}`}
            onClick={() => setIsSearchActive(!isSearchActive)}
            aria-label="Search in conversation"
            title="Search in conversation"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button> */}
          <div className="relative dropdown-toggle">
            <button
              className={`${isDark ? 'text-gray-300 hover:text-white' : 'text-gray-500 hover:text-gray-700'} p-1.5 rounded-full ${dropdownOpen ? 'bg-[#006060]/10' : ''}`}
              onClick={toggleDropdown}
              aria-label="Menu"
              title="Menu"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
            {dropdownOpen && (
              <div className={`absolute right-0 mt-2 w-52 ${isDark ? 'bg-[#2d3445] text-gray-200' : 'bg-white text-gray-700'} rounded-md shadow-lg z-10 border ${isDark ? 'border-[#384058]' : 'border-gray-200'}`}>
                <div className="py-1">
                  <button 
                    onClick={() => handleMenuSelect('info')} 
                    className={`block w-full text-left px-4 py-2 text-sm ${isDark ? 'hover:bg-[#1e232e]' : 'hover:bg-gray-100'}`}
                  >
                    {activeRoom.isGroup ? 'Group information' : 'Page information'}
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

                              
                                         
      {/* Group Info Drawer - Inline with chat */}
      {showInfoDrawer && (
        <div className="fixed inset-y-0 right-0 w-72 top-12 bg-white border-l border-gray-200 shadow-lg flex flex-col h-[calc(100%-3rem)] z-50">
          <div className="p-3 border-b border-gray-200 flex justify-between items-center bg-gray-50">
            <h2 className="text-base font-semibold text-gray-800">Group Information</h2>
            <button 
              onClick={() => setShowInfoDrawer(false)}
              className="p-1 rounded-full hover:bg-gray-200 transition-colors"
              aria-label="Close group info"
            >
              <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="flex-1 overflow-y-auto p-4">
            {loadingMembers ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-[#006060]"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Group Header */}
                <div className="text-center">
                  <div className="mx-auto h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center mb-3 overflow-hidden">
                    {activeRoom?.avatar ? (
                      <img 
                        src={activeRoom.avatar} 
                        alt={activeRoom.name} 
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/logo.png';
                        }}
                      />
                    ) : (
                      <span className="text-2xl font-bold text-gray-600">
                        {activeRoom?.name?.charAt(0)?.toUpperCase() || 'G'}
                      </span>
                    )}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">{activeRoom?.name || 'Group Chat'}</h3>
                  <div className="flex items-center justify-center space-x-2">
                    <span className="text-xs text-gray-500">{formatMemberCount(groupMembers.length || activeRoom?.memberCount || 0)} members</span>
                    <span className="px-2 py-0.5 text-xs font-medium bg-[#006060]/10 text-[#006060] rounded-full">
                      {activeRoom?.isGroup ? 'Group' : 'Direct'}
                    </span>
                  </div>
                </div>

                {/* Members Section */}
                <div>
                  <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Members</h4>
                  <div className="space-y-1.5">
                    {groupMembers.length > 0 ? (
                      groupMembers.map(member => (
                        <div
                          key={member.id}
                          className="flex items-center py-2 px-3 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-transparent transition-colors"
                          onClick={async () => {
                            // Prevent starting chat with self
                            if (member.id === currentUser?.id) return;
                            await startDirectChat({
                              id: member.id,
                              name: member.name,
                              avatar: member.avatar,
                              user_type: member.userType
                            });
                            setShowInfoDrawer(false);
                          }}
                        >
                          <div className="relative mr-3">
                            <img 
                              src={member.avatar || '/logo.png'} 
                              alt={member.name} 
                              className="w-8 h-8 rounded-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = '/logo.png';
                              }}
                            />
                            <div 
                              className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 ${isDark ? 'border-[#252B42]' : 'border-white'} ${
                                (state.activeRoomId && state.onlineUsers[state.activeRoomId]?.includes(member.id)) || member.isOnline ? 'bg-green-500' : 'bg-gray-400'
                              }`}
                              title={(state.activeRoomId && state.onlineUsers[state.activeRoomId]?.includes(member.id)) || member.isOnline ? 'Online' : 'Offline'}
                            />
                            {member.directChat && (
                              <div 
                                className={`absolute -bottom-1.5 -right-3.5 w-2 h-2 rounded-full border-2 ${isDark ? 'border-[#252B42]' : 'border-white'} bg-red-500`}
                                title="Direct chat"
                              />
                            )}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center space-x-1">
                              <p className={`text-sm ${isDark ? 'text-white' : 'text-gray-900'}`}>
                                {member.name}
                              </p>
                              <span className="ml-1.5 text-gray-400 group-hover:text-blue-500 transition-colors" title="Send direct message">
                                <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                                </svg>
                              </span>
                            </div>
                            <p className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                              {member.userType === 'teacher' ? 'Teacher' : 'Student'}
                            </p>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4">
                        <svg className="mx-auto h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        <p className="mt-2 text-sm text-gray-500">No members found</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <button 
              onClick={() => setShowLeaveConfirm(true)}
              className="w-full py-2 px-4 text-sm font-medium text-red-600 hover:bg-red-50 rounded-md transition-colors flex items-center justify-center space-x-2"
              disabled={isCurrentUserAdmin}
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <span>Leave Group</span>
            </button>
            {showLeaveConfirm && (
              <div className="fixed inset-0 flex items-center justify-center z-50">
                <div className="absolute inset-0 bg-black opacity-75"></div>
                <div className={`relative w-full max-w-md p-6 rounded-lg shadow-lg ${isDark ? 'bg-[#1e232e]' : 'bg-white'}`}>
                  <h3 className="text-lg font-medium mb-4">Leave Group</h3>
                  <p className="mb-6">Are you sure you want to leave this group? You won't be able to rejoin unless invited back by an admin.</p>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setShowLeaveConfirm(false)}
                      className={`px-4 py-2 rounded-md ${isDark ? 'bg-gray-700 hover:bg-gray-600' : 'bg-gray-200 hover:bg-gray-300'}`}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={async () => {
                        if (state.activeRoomId) {
                          const success = await leaveGroup(state.activeRoomId);
                          if (success) {
                            setShowInfoDrawer(false);
                            setShowLeaveConfirm(false);
                          }
                        }
                      }}
                      className="px-4 py-2 rounded-md bg-red-500 hover:bg-red-600 text-white"
                    >
                      Leave Group
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Messages Area */}
      <div className="flex-1 overflow-hidden p-4">
        {messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center px-4">
            <div className={`p-4 rounded-full ${isDark ? 'bg-[#2d3445]' : 'bg-gray-100'} mb-4`}>
              <svg className="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 className={`text-lg font-medium mb-1 ${isDark ? 'text-white' : 'text-gray-900'}`}>No messages yet</h3>
            <p className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
              Send a message to start the conversation
            </p>
          </div>
        ) : (
          <div className="h-full">
            <AutoSizer>
              {({ height, width }) => (
                <List
                  ref={listRef}
                  initialScrollOffset={(messages.length - 1) * 120}
                  height={height}
                  itemCount={displayItems.length}
                  itemSize={(index:number)=> displayItems[index].type==='header'? 36: 120} 
                  width={width}
                  itemData={{
                    messages,
                    displayItems,
                    currentUserId: currentUser?.id,
                    isDark,
                  }}
                >
                  {RowRenderer}
                </List>
              )}
            </AutoSizer>
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Invite to Group Modal */}
      {showInviteModal && activeRoom && (
        <InviteToGroupModal
          isOpen={showInviteModal}
          onClose={() => setShowInviteModal(false)}
          groupId={activeRoom.id}
          groupName={activeRoom.name}
        />
      )}

      {/* Bulk Invite Modal */}
      {showBulkInviteModal && activeRoom && (
        <BulkInviteModal
          isOpen={showBulkInviteModal}
          onClose={() => setShowBulkInviteModal(false)}
          groupId={activeRoom.id}
          groupName={activeRoom.name}
        />
      )}
    </div>
  );
};