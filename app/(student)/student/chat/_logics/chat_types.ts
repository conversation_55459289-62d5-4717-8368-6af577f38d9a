// Course Type
export interface Course {
  id: string;
  coursename: string;
}

// Create Chat Group Request
export interface CreateChatGroupRequest {
  name: string;
  description: string;
  type: string;
  course_id: string;
  is_private: boolean;
  initial_members: string[];
}

// Interface for the last message in a chat group
export interface ChatGroupLastMessage {
  content: string;
  timestamp: string;
  sender_name: string;
}

// Chat Group Response
export interface ChatGroup {
  id: string;
  name: string;
  description: string;
  type: string;
  course_id?: string;
  is_private: boolean;
  created_at: string;
  member_count?: number;
  members: ChatGroupMember[];
  last_message?: ChatGroupLastMessage | null;
}

// Chat Group Member
export interface ChatGroupMember {
  id: string;
  name: string;
  avatar?: string;
  is_admin?: boolean;
  student_id?: string;
  teacher_id?: string;
  isOnline?: boolean;
  user_type?: string;
}


// Chat Member (for members endpoint)
export interface ChatMember {
  id: string;
  name: string;
  avatar?: string;
  is_admin?: boolean;
  student_id?: string;
  teacher_id?: string;
  isOnline?: boolean;
  joined_at?: string;
  email?: string;
  user_type?: string;
}

// User Search Result
export interface UserSearchResult {
  id: string;
  name: string;
  email: string;
  type: 'student' | 'teacher';
  user_type: 'student' | 'teacher';
  _id: string;
}

// Invite to Group Modal Props
export interface InviteToGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: string;
  groupName: string;
}

// Bulk Invite Modal Props
export interface BulkInviteModalProps {
  isOpen: boolean;
  onClose: () => void;
  groupId: string;
  groupName: string;
}

// Interface for the response when fetching chat groups
export interface ChatGroupsResponse {
  data: ChatGroup[];
  total: number;
  page: number;
  limit: number;
}

// Interface for the response when fetching chat group members
export interface ChatGroupMembersResponse {
  data: ChatMember[];
  total: number;
}

export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read' | 'failed';

export interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  senderName?: string;
  senderType: string;
  senderAvatar?: string;
  content: string;
  messageType: string;
  attachments?: any[];
  timestamp: Date;
  readBy: string[];
  isRead?: boolean;
  status: MessageStatus;
}

export interface ChatRoom {
  id: string;
  name: string;
  description?: string;
  avatar?: string;
  lastMessage: string;
  timestamp: Date;
  unreadCount: number;
  isGroup: boolean;
  memberCount?: number;
  members: {
    id: string;
    name: string;
    avatar?: string;
    isAdmin?: boolean;
    isOnline?: boolean;
    user_type?: string;
  }[];
}

export interface ChatState {
  [x: string]: any;
  activeRoomId: string | null;
  rooms: ChatRoom[];
  messages: { [roomId: string]: ChatMessage[] };
  isLoading: boolean;
  error: string | null;
  onlineUsers: { [roomId: string]: string[] };
}

export interface ChatContextType {
  state: ChatState;
  setActiveRoom: (roomId: string) => Promise<void>;
  searchChats: (query: string) => void;
  refreshChatRooms: () => Promise<void>;
  fetchRoomMembers: (roomId: string) => Promise<ChatMember[]>;
  sendMessage: (message: string) => Promise<void>;
  addMessage: (message: ChatMessage) => void;
  updateUserPresence: (roomId: string, onlineUserIds: string[]) => void;
  /**
   * Initiate or navigate to a direct (one-on-one) chat with a recipient.
   * The recipient is uniquely identified by their user id. If a direct room already exists it will be reused,
   * otherwise a lightweight temporary room object will be created locally and WebSocket connection established.
   */
  startDirectChat: (recipient: { id: string; name: string; avatar?: string; user_type?: string }) => Promise<void>;
  currentUser?: {
    id: string;
    name: string;
    type: string;
    avatar?: string;
  };
  leaveGroup: (groupId: string) => Promise<boolean>;
}




export interface MessageRowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    messages: any[];
    currentUserId: string | undefined;
    isDark: boolean;
  };
}


  // Define member type with proper typing
  export interface GroupMember {
    directChat: any;
    id: string;
    name: string;
    avatar?: string;
    isOnline?: boolean;
    isAdmin?: boolean;
    userType?: 'student' | 'teacher' | 'admin';
    email?: string;
  }
