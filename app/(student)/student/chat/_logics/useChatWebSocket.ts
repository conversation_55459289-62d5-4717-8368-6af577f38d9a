import { useEffect, useCallback, useRef } from 'react';
import { WebSocketService } from './websocketService';
import { useChatContext } from './chat_context';
import { ChatMessageArea } from '../widgets/ChatMessageArea';

export const useChatWebSocket = (roomId: string | null) => {
  const { addMessage, updateUserPresence, state, currentUser } = useChatContext();
  const wsService = useRef<WebSocketService>(WebSocketService.getInstance());
  const isConnected = useRef(false);

  const handleNewMessage = useCallback((message: any) => {
    if (message.type === 'chat_message') {
      addMessage({
        id: message.id || Date.now().toString(),
        // Compute a consistent roomId for direct chats so both users reference the same room key
        roomId: (() => {
          let computedId = message.room_id as string | undefined;
          // If no room_id supplied (or it's a simple direct-<id> form) build a symmetrical id
          if (!computedId || /^direct-[\w-]+$/.test(computedId)) {
            if (message.sender_id && message.recipient_id) {
              const ids = [message.sender_id, message.recipient_id].sort();
              computedId = `direct-${ids.join('-')}`;
            }
          }
          return computedId || roomId || '';
        })(),
        senderId: message.sender_id,
        senderName: message.sender_name,
        senderType: message.sender_type,
        senderAvatar: message.sender_avatar,
        content: message.content,
        messageType: message.message_type,
        timestamp: new Date(message.timestamp || Date.now()),
        readBy: message.read_by || [],
        isRead: false,
        status: 'sent'
      });
    } else if (message.type === 'presence') {
      updateUserPresence(
        message.chat_group_id || roomId || '',
        message.online_users || []
      );
    } else if (message.type === 'system') {
      console.log('System message:', message.content);
    }
  }, [roomId, addMessage, updateUserPresence]);

  const handleConnectionChange = useCallback(() => {
    isConnected.current = true;
    console.log('WebSocket connected');
  }, []);

  // Connect to WebSocket when roomId changes
  useEffect(() => {
    if (!roomId) return;
    // Ensure we have the current user's name before attempting a direct connection
    if (!currentUser?.name && state.rooms.find(r => r.id === roomId && !r.isGroup)) {
      // Wait until currentUser is populated; effect will re-run when currentUser changes
      return;
    }

    const connect = async () => {
      try {
        // Determine if the room is a group or direct chat
        const room = state.rooms.find(r => r.id === roomId);
        if (!room) throw new Error('Room not found');

        if (room.isGroup) {
          await wsService.current.connectToGroupChat(roomId);
        } else {
          // For direct chat, identify the recipient (the member who is not the current user)
          const members = room.members || [];
          const currentUserId = state.currentUser?.id;
          const recipient = members.find(m => m.id !== currentUserId);
          if (!recipient) throw new Error('Recipient not found for direct chat');
          const senderName = currentUser?.name || state.currentUser?.name || 'Unknown';
          await wsService.current.connectToDirectChat(recipient.id, recipient.user_type || 'student', senderName);
        }
        wsService.current.addMessageHandler(handleNewMessage);
        wsService.current.addConnectionHandler(handleConnectionChange);
      } catch (error) {
        console.error('Failed to connect to WebSocket:', error);
      }
    };

    connect();

    return () => {
      wsService.current.removeMessageHandler(handleNewMessage);
      wsService.current.removeConnectionHandler(handleConnectionChange);
      wsService.current.disconnect();
      isConnected.current = false;
    };
  }, [roomId, handleNewMessage, handleConnectionChange]);

  const sendChatMessage = useCallback(async (content: string) => {
    if (!isConnected.current || !roomId) {
      throw new Error('Not connected to chat or no room selected');
    }
    await wsService.current.sendMessage({
      type: 'chat_message',
      content,
      roomId,
      timestamp: new Date().toISOString()
    });
  }, [roomId]);

  return {
    isConnected: isConnected.current,
    sendMessage: sendChatMessage,
  };
};
